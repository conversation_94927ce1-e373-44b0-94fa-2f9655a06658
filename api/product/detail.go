package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type ProductDetailRequest struct {
	UserCategoryID int `json:"user_category_id" query:"user_category_id" form:"user_category_id"`
	ProductID      int `json:"product_id" query:"product_id" form:"product_id"`
}

func ProductDetailHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		req := new(ProductDetailRequest)
		if err := c.Bind(req); err != nil {
			return c.JSON(http.StatusBadRequest, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "Invalid request payload",
			})
		}

		// Simple validation
		if req.UserCategoryID == 0 || req.ProductID == 0 {
			return c.JSON(http.StatusBadRequest, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusBadRequest,
				Error:      true,
				Message:    "user_category_id and product_id are required",
			})
		}

		tx := db.Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		query := NewProductQuery(tx)
		var q *gorm.DB

		switch req.UserCategoryID {
		case 1:
			q = query.AhliPriceFeatured()
		case 2:
			q = query.AhliPriceFeatured()
		case 3:
			q = query.AhliPriceFeatured()
		case 4:
			q = query.AhliPriceFeatured()
		case 5:
			q = query.AhliPriceFeatured()
		default:
			tx.Rollback()
			return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid user_category_id"})
		}

		// Query for the specific product
		var productDetail map[string]interface{}
		if err := q.Where("data_database.id = ?", req.ProductID).First(&productDetail).Error; err != nil {
			tx.Rollback()
			if err == gorm.ErrRecordNotFound {
				return c.JSON(http.StatusNotFound, ApiResponse[any]{
					Status:     "Error",
					StatusCode: http.StatusNotFound,
					Error:      true,
					Message:    "Product not found",
				})
			}
			return c.JSON(http.StatusInternalServerError, ApiResponse[any]{
				Status:     "Error",
				StatusCode: http.StatusInternalServerError,
				Error:      true,
				Message:    err.Error(),
			})
		}

		tx.Commit()

		data := map[string]any{
			"product": productDetail,
		}
		return c.JSON(http.StatusOK, ApiResponse[map[string]any]{
			Status:     "Success",
			StatusCode: http.StatusOK,
			Error:      false,
			Message:    "",
			Data:       data,
		})
	}
}
